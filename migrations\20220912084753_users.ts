import { Knex } from 'knex'

export async function up (knex: Knex): Promise<void> {
  return knex.schema.createTable('users', (table) => {
    table.string('id').primary()
    table.string('email').notNullable()
    table.string('full_name').notNullable()
    table.dateTime('created_at').notNullable()
    table.dateTime('updated_at').notNullable()
    table.dateTime('deleted_at')
  })
}

export async function down (knex: Knex): Promise<void> {
  return knex.schema.dropTable('users')
}

