package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type UserCreate struct {
	core.BaseValidator
	Email    *string `json:"email"`
	FullName *string `json:"full_name"`
}

func (r *UserCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>(r.Email, "email"))
	r.Must(r.<PERSON>tr<PERSON>equired(r.<PERSON>ail, "email"))
	r.Must(r.Is<PERSON>trUnique(ctx, r.Email, models.User{}.TableName(), "email", "", "email"))

	r.Must(r.<PERSON>equired(r.<PERSON>ame, "full_name"))

	return r.<PERSON>rror()
}
