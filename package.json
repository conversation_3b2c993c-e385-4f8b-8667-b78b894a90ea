{"name": "golang-template", "version": "1.0.0", "main": "index.js", "repository": "https://gitlab.finema.co/finema/finework/finework-api", "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"migrate": "knex migrate:latest --env production", "seed": "knex seed:run --env production", "rollback": "knex migrate:rollback --env production"}, "dependencies": {"dotenv": "^17.2.1", "knex": "^3.1.0", "mysql": "^2.18.1", "tedious": "^19.0.0"}, "devDependencies": {"@babel/register": "^7.12.1", "@types/node": "^22.17.0", "sucrase": "^3.16.0", "ts-node": "^10.9.2", "typescript": "^5.4.4", "typescript-require": "^0.3.0"}}