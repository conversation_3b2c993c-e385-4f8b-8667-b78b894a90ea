package cmd

import (
	"gitlab.finema.co/finema/finework/finework-api/modules/home"
	core "gitlab.finema.co/finema/idin-core"
)

func APIRun() {
	env := core.NewEnv()
	//db, err := core.NewDatabase(env.Config()).Connect()
	//if err != nil {
	//	fmt.Fprintf(os.Stderr, "MySQL: %v", err)
	//	os.Exit(1)
	//}

	e := core.NewHTTPServer(&core.HTTPContextOptions{
		ContextOptions: &core.ContextOptions{
			//DB:  db,
			ENV: env,
		},
	})

	//e.Pre(middleware.Rewrite(map[string]string{
	//	"/api/*": "/$1",
	//}))

	home.NewHomeHTTP(e)
	//user.NewUserHTTP(e)

	core.StartHTTPServer(e, env)
}
