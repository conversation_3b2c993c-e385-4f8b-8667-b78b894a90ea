package user

import (
	"github.com/labstack/echo/v4"
	core "gitlab.finema.co/finema/idin-core"
)

func NewUserHTTP(e *echo.Echo) {
	user := &UserController{}
	e.GET("/users", core.WithHTTPContext(user.Pagination))
	e.GET("/users/:id", core.WithHTTPContext(user.Find))
	e.POST("/users", core.WithHTTPContext(user.Create))
	e.PUT("/users/:id", core.WithHTTPContext(user.Update))
	e.DELETE("/users/:id", core.WithHTTPContext(user.Delete))
}
