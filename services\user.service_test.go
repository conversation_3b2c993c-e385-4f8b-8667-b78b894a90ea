package services

import (
	"path"
	"path/filepath"
	"runtime"
	"testing"

	"github.com/stretchr/testify/mock"
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	"gitlab.finema.co/finema/idin-core/repository"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	core "gitlab.finema.co/finema/idin-core"
)

type UserServiceSuite struct {
	suite.Suite
	ctx core.IE2EContext
}

func TestUserServiceSuite(t *testing.T) {
	suite.Run(t, new(UserServiceSuite))
}

func (suite *UserServiceSuite) SetupTest() {
	// Setup test dependencies and initialize the svc
	suite.ctx = createE2EContext() // Create a mock implementation of core.IContext

}
func (suite *UserServiceSuite) TestCreate() {
	// Prepare test data
	payload := &UserCreatePayload{
		Email:    "<EMAIL>",
		FullName: "<PERSON>",
	}

	userMock := repository.NewMock[models.User]()
	userMock.On("Create", mock.Anything).Return(nil)
	userMock.On("FindOne", mock.Anything, mock.Anything).Return(&models.User{
		BaseModel: models.NewBaseModel(),
		Email:     payload.Email,
		FullName:  payload.FullName,
	}, nil)

	repo.User = func(c core.IContext, options ...repo.UserOption) repository.IRepository[models.User] {
		return userMock
	}

	svc := NewUserService(suite.ctx)

	// Set the expectations for the mock UserService
	user, ierr := svc.Create(payload)

	// Assert the result
	assert.NoError(suite.T(), ierr)
	assert.NotNil(suite.T(), user)
	assert.Equal(suite.T(), payload.Email, user.Email)
}

func (suite *UserServiceSuite) TestUpdate() {
	// Prepare test data
	baseModel := models.NewBaseModel()

	id := baseModel.ID
	payload := &UserUpdatePayload{
		FullName: "John Smith",
	}

	userMock := repository.NewMock[models.User]()
	userMock.On("FindOne", "id = ?", id).Return(&models.User{
		BaseModel: baseModel,
		Email:     "<EMAIL>",
		FullName:  "John Doe",
	}, nil)
	userMock.On("Where", "id = ?", id).Return(nil)
	userMock.On("Updates", mock.Anything).Return(nil)
	userMock.On("FindOne", "id = ?", id).Return(&models.User{
		BaseModel: baseModel,
		Email:     "<EMAIL>",
		FullName:  payload.FullName,
	}, nil)

	repo.User = func(c core.IContext, options ...repo.UserOption) repository.IRepository[models.User] {
		return userMock
	}

	svc := NewUserService(suite.ctx)

	// Set the expectations for the mock UserService
	user, ierr := svc.Update(id, payload)

	// Assert the result
	assert.NoError(suite.T(), ierr)
	assert.NotNil(suite.T(), user)
	assert.Equal(suite.T(), payload.FullName, user.FullName)
}

func (suite *UserServiceSuite) TestFind() {
	// Prepare test data\
	baseModel := models.NewBaseModel()
	id := baseModel.ID

	userMock := repository.NewMock[models.User]()
	userMock.On("FindOne", "id = ?", id).Return(&models.User{
		BaseModel: baseModel,
		Email:     "<EMAIL>",
		FullName:  "John Doe",
	}, nil)

	repo.User = func(c core.IContext, options ...repo.UserOption) repository.IRepository[models.User] {
		return userMock
	}

	svc := NewUserService(suite.ctx)

	// Set the expectations for the mock UserService
	user, ierr := svc.Find(id)

	// Assert the result
	assert.NoError(suite.T(), ierr)
	assert.NotNil(suite.T(), user)
	assert.Equal(suite.T(), "<EMAIL>", user.Email)
	assert.Equal(suite.T(), "John Doe", user.FullName)
}

func (suite *UserServiceSuite) TestPagination() {
	// Prepare test data
	pageOptions := &core.PageOptions{
		Page:  1,
		Limit: 10,
	}

	paginationMock := &repository.Pagination[models.User]{
		Limit: pageOptions.Limit,
		Page:  pageOptions.Page,
		Total: 2,
		Count: 2,
		Items: []models.User{
			{BaseModel: models.NewBaseModel(), Email: "<EMAIL>", FullName: "User 1"},
			{BaseModel: models.NewBaseModel(), Email: "<EMAIL>", FullName: "User 2"},
		},
	}

	userMock := repository.NewMock[models.User]()
	userMock.On("Pagination", pageOptions).Return(paginationMock, nil)

	repo.User = func(c core.IContext, options ...repo.UserOption) repository.IRepository[models.User] {
		return userMock
	}

	svc := NewUserService(suite.ctx)

	// Set the expectations for the mock UserService
	pagination, ierr := svc.Pagination(pageOptions)

	// Assert the result
	assert.NoError(suite.T(), ierr)
	assert.NotNil(suite.T(), pagination)
	assert.Equal(suite.T(), pageOptions.Limit, pagination.Limit)
	assert.Equal(suite.T(), pageOptions.Page, pagination.Page)
	assert.Equal(suite.T(), paginationMock.Total, pagination.Total)
	assert.Equal(suite.T(), paginationMock.Count, pagination.Count)
	assert.Len(suite.T(), pagination.Items, len(paginationMock.Items))
	assert.Equal(suite.T(), "<EMAIL>", pagination.Items[0].Email)
	assert.Equal(suite.T(), "<EMAIL>", pagination.Items[1].Email)
}

func (suite *UserServiceSuite) TestDelete() {
	// Prepare test data
	baseModel := models.NewBaseModel()
	id := baseModel.ID

	userMock := repository.NewMock[models.User]()
	userMock.On("FindOne", "id = ?", id).Return(&models.User{
		BaseModel: baseModel,
		Email:     "<EMAIL>",
		FullName:  "John Doe",
	}, nil)
	userMock.On("Delete", "id = ?", id).Return(nil)

	repo.User = func(c core.IContext, options ...repo.UserOption) repository.IRepository[models.User] {
		return userMock
	}

	svc := NewUserService(suite.ctx)

	// Set the expectations for the mock UserService
	ierr := svc.Delete(id)

	// Assert the result
	assert.NoError(suite.T(), ierr)
}
func createE2EContext() core.IE2EContext {
	// Create a mock implementation of core.IContext for testing purposes
	// Return the mock implementation
	env := core.NewENVPath(rootDir())

	return core.NewE2EContext(&core.E2EContextOptions{
		ContextOptions: &core.ContextOptions{
			ENV: env,
		},
	})
}

func rootDir() string {
	_, b, _, _ := runtime.Caller(0)
	d := path.Join(path.Dir(b))
	return filepath.Dir(d)
}
